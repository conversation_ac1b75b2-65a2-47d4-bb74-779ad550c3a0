{"error":"connect ECONNREFUSED ::1:27017, connect ECONNR<PERSON>USED 127.0.0.1:27017","level":"error","message":"Database Connection Failed","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDB (D:\\Yash\\blog\\rivsy\\server\\index.js:136:18)\n    at async startServer (D:\\Yash\\blog\\rivsy\\server\\index.js:161:5)","timestamp":"2025-07-01 20:03:43:343"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNR<PERSON>US<PERSON> 127.0.0.1:27017","level":"error","message":"Database Connection Failed","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDB (D:\\Yash\\blog\\rivsy\\server\\index.js:136:18)\n    at async startServer (D:\\Yash\\blog\\rivsy\\server\\index.js:158:5)","timestamp":"2025-07-01 20:04:52:452"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"Database Connection Failed","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDB (D:\\Yash\\blog\\rivsy\\server\\index.js:136:18)\n    at async startServer (D:\\Yash\\blog\\rivsy\\server\\index.js:158:5)","timestamp":"2025-07-01 20:11:45:1145"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"Database Connection Failed","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDB (D:\\Yash\\blog\\rivsy\\server\\index.js:143:18)\n    at async startServer (D:\\Yash\\blog\\rivsy\\server\\index.js:177:5)","timestamp":"2025-07-01 20:13:52:1352"}
{"level":"warn","message":"Continuing in development mode without database connection.","timestamp":"2025-07-01 20:13:52:1352"}
{"clientUrl":"http://localhost:3000","environment":"development","healthCheck":"http://localhost:5000/health","level":"info","message":"Server Started Successfully","port":"5000","timestamp":"2025-07-01 20:13:52:1352"}
{"level":"warn","message":"No MongoDB URI provided. Running in development mode without database.","timestamp":"2025-07-01 20:14:27:1427"}
{"clientUrl":"http://localhost:3000","environment":"development","healthCheck":"http://localhost:5000/health","level":"info","message":"Server Started Successfully","port":"5000","timestamp":"2025-07-01 20:14:27:1427"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"Database Connection Failed","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDB (D:\\Yash\\blog\\rivsy\\server\\index.js:154:18)\n    at async startServer (D:\\Yash\\blog\\rivsy\\server\\index.js:188:5)","timestamp":"2025-07-01 20:14:30:1430"}
{"level":"info","message":"Cache Warmed Successfully","timestamp":"2025-07-01 20:14:37:1437"}
{"level":"error","message":"Application Error The \"time\" argument must be an instance of Array. Received an instance of Date","request":{"ip":"::1","method":"GET","url":"/health","userAgent":"curl/8.9.1"},"requestId":"mckyv8wnydvb1nhxhio","stack":"TypeError [ERR_INVALID_ARG_TYPE]: The \"time\" argument must be an instance of Array. Received an instance of Date\n    at process.hrtime (node:internal/process/per_thread:80:5)\n    at res.end (D:\\Yash\\blog\\rivsy\\server\\middleware\\logging.js:75:26)\n    at ServerResponse.send (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\response.js:232:10)\n    at ServerResponse.json (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\response.js:278:15)\n    at D:\\Yash\\blog\\rivsy\\server\\index.js:103:19\n    at Layer.handle [as handle_request] (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\router\\index.js:284:15","timestamp":"2025-07-01 20:14:46:1446"}
{"level":"warn","message":"No MongoDB URI provided. Running in development mode without database.","timestamp":"2025-07-01 20:15:20:1520"}
{"clientUrl":"http://localhost:3000","environment":"development","healthCheck":"http://localhost:5000/health","level":"info","message":"Server Started Successfully","port":"5000","timestamp":"2025-07-01 20:15:20:1520"}
{"level":"info","message":"Cache Warmed Successfully","timestamp":"2025-07-01 20:15:30:1530"}
{"ip":"::1","level":"http","message":"API Request","method":"GET","responseTime":"5ms","status":200,"timestamp":"2025-07-01 20:16:55:1655","url":"/health"}
{"level":"error","message":"Application Error Cannot set headers after they are sent to the client","request":{"ip":"::1","method":"GET","url":"/health"},"requestId":"mckyy0leh4ia5e2idq","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at new NodeError (node:internal/errors:405:5)\n    at ServerResponse.setHeader (node:_http_outgoing:648:11)\n    at ServerResponse.header (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\response.js:794:10)\n    at res.end (D:\\Yash\\blog\\rivsy\\server\\services\\performanceService.js:54:13)\n    at ServerResponse.end (D:\\Yash\\blog\\rivsy\\server\\node_modules\\compression\\index.js:128:21)\n    at res.end (D:\\Yash\\blog\\rivsy\\server\\middleware\\logging.js:88:17)\n    at ServerResponse.send (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\response.js:232:10)\n    at ServerResponse.json (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\response.js:278:15)\n    at D:\\Yash\\blog\\rivsy\\server\\index.js:103:19\n    at Layer.handle [as handle_request] (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-01 20:16:55:1655"}
{"errorType":"Error","level":"error","message":"Application Error Cannot set headers after they are sent to the client","request":{"ip":"::1","method":"GET","url":"/health"},"requestId":"mckyy0leh4ia5e2idq","stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at new NodeError (node:internal/errors:405:5)\n    at ServerResponse.setHeader (node:_http_outgoing:648:11)\n    at ServerResponse.header (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\response.js:794:10)\n    at res.end (D:\\Yash\\blog\\rivsy\\server\\services\\performanceService.js:54:13)\n    at ServerResponse.end (D:\\Yash\\blog\\rivsy\\server\\node_modules\\compression\\index.js:128:21)\n    at res.end (D:\\Yash\\blog\\rivsy\\server\\middleware\\logging.js:88:17)\n    at ServerResponse.send (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\response.js:232:10)\n    at ServerResponse.json (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\response.js:278:15)\n    at D:\\Yash\\blog\\rivsy\\server\\index.js:103:19\n    at Layer.handle [as handle_request] (D:\\Yash\\blog\\rivsy\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)","statusCode":500,"timestamp":"2025-07-01 20:16:55:1655"}
{"level":"warn","message":"No MongoDB URI provided. Running in development mode without database.","timestamp":"2025-07-01 20:17:50:1750"}
{"clientUrl":"http://localhost:3000","environment":"development","healthCheck":"http://localhost:5000/health","level":"info","message":"Server Started Successfully","port":"5000","timestamp":"2025-07-01 20:17:50:1750"}
{"level":"info","message":"Cache Warmed Successfully","timestamp":"2025-07-01 20:18:01:181"}
{"level":"warn","message":"No MongoDB URI provided. Running in development mode without database.","timestamp":"2025-07-01 20:18:05:185"}
{"clientUrl":"http://localhost:3000","environment":"development","healthCheck":"http://localhost:5000/health","level":"info","message":"Server Started Successfully","port":"5000","timestamp":"2025-07-01 20:18:05:185"}
{"level":"info","message":"Cache Warmed Successfully","timestamp":"2025-07-01 20:18:15:1815"}
{"level":"warn","message":"No MongoDB URI provided. Running in development mode without database.","timestamp":"2025-07-01 20:20:24:2024"}
{"clientUrl":"http://localhost:3000","environment":"development","healthCheck":"http://localhost:5000/health","level":"info","message":"Server Started Successfully","port":"5000","timestamp":"2025-07-01 20:20:24:2024"}
{"level":"info","message":"Cache Warmed Successfully","timestamp":"2025-07-01 20:20:34:2034"}
{"ip":"::1","level":"http","message":"API Request","method":"GET","responseTime":"4ms","status":200,"timestamp":"2025-07-01 20:20:49:2049","url":"/health","userAgent":"curl/8.9.1"}
{"level":"http","message":"GET /health 200 12.00 ms - 248","timestamp":"2025-07-01 20:20:49:2049"}
{"ip":"::1","level":"http","message":"API Request","method":"GET","responseTime":"1ms","status":200,"timestamp":"2025-07-01 20:21:00:210","url":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Microsoft Windows 10.0.19045; en-US) PowerShell/7.5.1"}
{"level":"http","message":"GET /health 200 4.00 ms - 248","timestamp":"2025-07-01 20:21:00:210"}
{"ip":"::1","level":"http","message":"API Request","method":"GET","responseTime":"1ms","status":200,"timestamp":"2025-07-01 20:21:09:219","url":"/health"}
{"level":"http","message":"GET /health 200 5.00 ms - 248","timestamp":"2025-07-01 20:21:09:219"}
{"level":"http","message":"GET /api/posts - 5002.00 ms - -","timestamp":"2025-07-01 20:21:15:2115"}
{"level":"error","message":"Application Error Operation `posts.find()` buffering timed out after 10000ms","request":{"ip":"::1","method":"GET","url":"/api/posts"},"requestId":"mckz3gz4brtmtm1uylv","stack":"MongooseError: Operation `posts.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-07-01 20:21:20:2120"}
{"errorType":"MongooseError","level":"error","message":"Application Error Operation `posts.find()` buffering timed out after 10000ms","request":{"ip":"::1","method":"GET","url":"/api/posts"},"requestId":"mckz3gz4brtmtm1uylv","stack":"MongooseError: Operation `posts.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (D:\\Yash\\blog\\rivsy\\server\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","statusCode":500,"timestamp":"2025-07-01 20:21:20:2120"}
{"ip":"::1","level":"http","message":"API Request","method":"GET","responseTime":"10021ms","status":500,"timestamp":"2025-07-01 20:21:20:2120","url":"/api/posts"}
