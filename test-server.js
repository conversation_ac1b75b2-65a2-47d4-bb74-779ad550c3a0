#!/usr/bin/env node

const http = require('http');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const testEndpoint = (url, description) => {
  return new Promise((resolve) => {
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          log(`✅ ${description}: OK (${res.statusCode})`, 'green');
          
          if (url.includes('/health')) {
            log(`   Database: ${jsonData.database?.status || 'unknown'}`, 'blue');
            log(`   Features: ${Object.keys(jsonData.features || {}).join(', ')}`, 'blue');
          }
          
          resolve({ success: true, status: res.statusCode, data: jsonData });
        } catch (error) {
          log(`⚠️  ${description}: Response not JSON (${res.statusCode})`, 'yellow');
          resolve({ success: true, status: res.statusCode, data: data });
        }
      });
    });
    
    req.on('error', (error) => {
      log(`❌ ${description}: Failed - ${error.message}`, 'red');
      resolve({ success: false, error: error.message });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      log(`❌ ${description}: Timeout`, 'red');
      resolve({ success: false, error: 'Timeout' });
    });
  });
};

const main = async () => {
  log('🧪 Testing Rivsy Blog Platform Server', 'bright');
  log('=====================================', 'bright');
  
  const tests = [
    { url: 'http://localhost:5000/health', description: 'Health Check' },
    { url: 'http://localhost:5000/api/posts', description: 'Posts API' },
    { url: 'http://localhost:3000', description: 'Frontend (React)' },
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    const result = await testEndpoint(test.url, test.description);
    if (result.success) {
      passedTests++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  log('\n📊 Test Results:', 'bright');
  log(`Passed: ${passedTests}/${totalTests}`, passedTests === totalTests ? 'green' : 'yellow');
  
  if (passedTests === totalTests) {
    log('\n🎉 All systems operational!', 'green');
    log('Your Rivsy Blog Platform is ready to use.', 'green');
  } else {
    log('\n⚠️  Some services may not be running.', 'yellow');
    log('Check that both server and client are started:', 'yellow');
    log('  Server: npm run dev (in server directory)', 'blue');
    log('  Client: npm run dev (in client directory)', 'blue');
  }
  
  log('\n🔗 Available URLs:', 'cyan');
  log('  Frontend: http://localhost:3000', 'blue');
  log('  Backend:  http://localhost:5000', 'blue');
  log('  Health:   http://localhost:5000/health', 'blue');
  log('  API:      http://localhost:5000/api/posts', 'blue');
};

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testEndpoint };
