# 🚀 MongoDB Setup Guide for Rivsy Blog Platform

## ✅ **Current Status: SERVER IS RUNNING!**

Your server is now successfully running at **http://localhost:5000** without requiring MongoDB to be installed locally. The application has been configured to work in development mode without a database connection.

## 🎯 **Quick Test**

Your server is working! You can test it by visiting:
- **Health Check**: http://localhost:5000/health
- **Frontend**: http://localhost:3000 (if client is running)

## 📊 **Database Options**

Choose one of the following options to add database functionality:

### Option 1: MongoDB Atlas (Cloud - Recommended) ⭐

**Pros**: Free, no local installation, automatic backups, scalable
**Cons**: Requires internet connection

#### Steps:
1. **Create Account**: Go to [MongoDB Atlas](https://cloud.mongodb.com/)
2. **Create Free Cluster**:
   - Click "Build a Database"
   - Choose "FREE" tier (M0 Sandbox)
   - Select a cloud provider and region
   - Name your cluster (e.g., "rivsy-cluster")
3. **Create Database User**:
   - Go to "Database Access"
   - Click "Add New Database User"
   - Username: `rivsy-user`
   - Password: Generate a secure password
   - Database User Privileges: "Read and write to any database"
4. **Whitelist IP Address**:
   - Go to "Network Access"
   - Click "Add IP Address"
   - Choose "Allow Access from Anywhere" (0.0.0.0/0) for development
5. **Get Connection String**:
   - Go to "Database" → "Connect"
   - Choose "Connect your application"
   - Copy the connection string
6. **Update Environment**:
   ```bash
   # Edit server/.env
   MONGODB_URI=mongodb+srv://rivsy-user:<EMAIL>/rivsy-blog?retryWrites=true&w=majority
   ```

### Option 2: Local MongoDB Installation

**Pros**: Works offline, full control
**Cons**: Requires installation and setup

#### Windows Installation:
1. **Download**: Go to [MongoDB Community Server](https://www.mongodb.com/try/download/community)
2. **Install**: Run the installer with default settings
3. **Start Service**: MongoDB should start automatically as a Windows service
4. **Verify**: Open Command Prompt and run `mongod --version`
5. **Update Environment**:
   ```bash
   # Edit server/.env
   MONGODB_URI=mongodb://localhost:27017/rivsy-blog
   ```

#### Alternative - MongoDB with Docker:
```bash
# Install Docker Desktop first, then run:
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Update server/.env
MONGODB_URI=mongodb://localhost:27017/rivsy-blog
```

### Option 3: Continue Without Database (Current Setup)

**Pros**: No setup required, server runs immediately
**Cons**: Limited functionality (no data persistence)

Your server is already configured to run without MongoDB for development purposes.

## 🔧 **Enabling Database Connection**

Once you have MongoDB set up (Atlas or local):

1. **Update Environment Variables**:
   ```bash
   # Edit server/.env
   # Uncomment and update the MONGODB_URI line:
   MONGODB_URI=your_connection_string_here
   ```

2. **Restart Server**: The server will automatically detect the database and connect

3. **Verify Connection**: Check http://localhost:5000/health - it should show database status as "connected"

## 🧪 **Testing Database Connection**

After setting up MongoDB, test the connection:

```bash
# Check health endpoint
curl http://localhost:5000/health

# Expected response should include:
{
  "database": {
    "status": "connected",
    "uri": "configured"
  }
}
```

## 🚨 **Troubleshooting**

### Common Issues:

1. **Connection Timeout**:
   - Check internet connection (for Atlas)
   - Verify IP whitelist (for Atlas)
   - Ensure MongoDB service is running (for local)

2. **Authentication Failed**:
   - Double-check username/password
   - Ensure user has proper permissions

3. **Network Issues**:
   - Check firewall settings
   - Verify port 27017 is not blocked

### Getting Help:

If you encounter issues:
1. Check the server logs for detailed error messages
2. Verify your connection string format
3. Test connection using MongoDB Compass (GUI tool)

## 🎉 **Next Steps**

Once your database is connected:

1. **Test the Application**: Create posts, users, etc.
2. **Run Tests**: `node test-runner.js`
3. **Set up Clerk Authentication**: Follow the Clerk setup guide
4. **Deploy to Production**: Consider using MongoDB Atlas for production

## 📝 **Environment File Template**

Here's a complete `.env` template for your server:

```env
# Database (choose one)
MONGODB_URI=mongodb+srv://username:<EMAIL>/rivsy-blog
# MONGODB_URI=mongodb://localhost:27017/rivsy-blog

# Server Configuration
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# JWT Secrets
JWT_SECRET=your-super-secret-jwt-key-for-development
REFRESH_TOKEN_SECRET=your-super-secret-refresh-token-key-for-development

# Clerk Authentication (optional)
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key_here
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here

# Security
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

---

**Your Rivsy Blog Platform is ready to go! 🚀**
